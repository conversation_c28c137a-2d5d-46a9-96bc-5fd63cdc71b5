from http.server import BaseHTTPRequestHandler
import json
import os
import requests
import time
import hashlib
from urllib.parse import parse_qs, urlparse
from datetime import datetime

# Configuration
AIRTABLE_BASE_URL = 'https://api.airtable.com/v0'
AIRTABLE_API_TIMEOUT = 15
MAX_TOTAL_RECORDS = 10000
MAX_PAGINATION_PAGES = 100

# Simple in-memory cache for the serverless function
cache = {}
cache_timestamps = {}

def validate_airtable_params(base_id, table_id, max_records_str, filter_formula):
    """Validate and sanitize input parameters for Airtable API requests."""
    errors = []

    # Validate base_id format
    if not base_id:
        errors.append("baseId is required")
    elif not base_id.startswith('app') or len(base_id) != 17:
        errors.append("baseId must start with 'app' and be 17 characters long")

    # Validate table_id format
    if not table_id:
        errors.append("tableId is required")
    elif not table_id.startswith('tbl') or len(table_id) != 17:
        errors.append("tableId must start with 'tbl' and be 17 characters long")

    # Validate max_records
    max_records = None
    if max_records_str:
        try:
            max_records = int(max_records_str)
            if max_records <= 0:
                errors.append("maxRecords must be a positive integer")
            elif max_records > MAX_TOTAL_RECORDS:
                errors.append(f"maxRecords cannot exceed {MAX_TOTAL_RECORDS:,}")
        except ValueError:
            errors.append("maxRecords must be a valid integer")

    # Validate filter formula
    if filter_formula:
        dangerous_patterns = ['javascript:', 'eval(', 'script>', 'DROP TABLE', 'DELETE FROM']
        filter_lower = filter_formula.lower()
        for pattern in dangerous_patterns:
            if pattern in filter_lower:
                errors.append(f"Filter formula contains dangerous pattern: {pattern}")
        
        if len(filter_formula) > 1000:
            errors.append("Filter formula is too long (max 1000 characters)")

    if errors:
        return None, "; ".join(errors)

    return {
        'base_id': base_id,
        'table_id': table_id,
        'max_records': max_records,
        'filter_formula': filter_formula
    }, None

def get_cache_key(base_id, table_id, filter_formula, max_records):
    """Generate cache key for request"""
    key_data = f"{base_id}:{table_id}:{filter_formula or ''}:{max_records}"
    return hashlib.md5(key_data.encode()).hexdigest()

def get_cached_data(cache_key, ttl_seconds=300):
    """Get cached data if valid"""
    if cache_key not in cache:
        return None
    
    cache_time = cache_timestamps.get(cache_key, 0)
    if time.time() - cache_time > ttl_seconds:
        # Remove expired entry
        if cache_key in cache:
            del cache[cache_key]
        if cache_key in cache_timestamps:
            del cache_timestamps[cache_key]
        return None
    
    return cache[cache_key]

def set_cached_data(cache_key, data):
    """Store data in cache"""
    cache[cache_key] = data
    cache_timestamps[cache_key] = time.time()

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            # Parse URL and query parameters
            parsed_url = urlparse(self.path)
            query_params = parse_qs(parsed_url.query)
            
            # Extract parameters
            base_id = query_params.get('baseId', [None])[0]
            table_id = query_params.get('tableId', [None])[0]
            max_records_str = query_params.get('maxRecords', [None])[0]
            filter_formula = query_params.get('filterByFormula', [None])[0]

            # Validate parameters
            validated_params, error_msg = validate_airtable_params(
                base_id, table_id, max_records_str, filter_formula
            )

            if error_msg:
                self.send_response(400)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps({"error": f"Validation error: {error_msg}"}).encode())
                return

            # Extract validated parameters
            base_id = validated_params['base_id']
            table_id = validated_params['table_id']
            max_records = validated_params['max_records']
            filter_formula = validated_params['filter_formula']

            # Check cache
            cache_key = get_cache_key(base_id, table_id, filter_formula, max_records)
            cache_ttl = 60 if filter_formula else 300
            cached_data = get_cached_data(cache_key, cache_ttl)

            if cached_data:
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(cached_data).encode())
                return

            # Fetch from Airtable
            airtable_api_key = os.environ.get('AIRTABLE_API_KEY')
            if not airtable_api_key:
                raise ValueError("AIRTABLE_API_KEY environment variable is required")

            url = f"{AIRTABLE_BASE_URL}/{base_id}/{table_id}"
            headers = {
                'Authorization': f'Bearer {airtable_api_key}',
                'Content-Type': 'application/json'
            }

            # Fetch all records with pagination
            all_records = []
            offset = None
            page_count = 0

            while page_count < MAX_PAGINATION_PAGES:
                page_count += 1
                page_params = {}

                if offset:
                    page_params['offset'] = offset

                if max_records is not None:
                    remaining_records = max_records - len(all_records)
                    if remaining_records <= 0:
                        break
                    page_params['maxRecords'] = min(100, remaining_records)

                if filter_formula:
                    page_params['filterByFormula'] = filter_formula

                response = requests.get(url, headers=headers, params=page_params, timeout=AIRTABLE_API_TIMEOUT)
                response.raise_for_status()

                data = response.json()
                page_records = data.get('records', [])

                # Flatten records
                for record in page_records:
                    flattened_record = {
                        'id': record['id'],
                        'createdTime': record['createdTime'],
                        **record['fields']
                    }
                    all_records.append(flattened_record)

                offset = data.get('offset')
                if not offset:
                    break

            # Prepare response
            response_data = {
                'records': all_records,
                'offset': None,
                'pagination_info': {
                    'total_records': len(all_records),
                    'pages_fetched': page_count,
                    'server_side_pagination': True
                }
            }

            # Cache the response
            set_cached_data(cache_key, response_data)

            # Send response
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response_data).encode())

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
