# 🚀 Netlify Deployment Guide for Attribution Dashboard

This guide will help you deploy your Flask-based Attribution Dashboard to Netlify using serverless functions.

## 📋 Prerequisites

1. **Netlify Account**: Sign up at [netlify.com](https://netlify.com)
2. **Git Repository**: Your code should be in a Git repository (GitHub, GitLab, etc.)
3. **API Keys**: Your Claude and Airtable API keys

## 🔧 Step 1: Prepare Your Repository

### 1.1 Install Dependencies
```bash
npm install
```

### 1.2 Test Locally (Optional)
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Start local development server
netlify dev
```

## 🌐 Step 2: Deploy to Netlify

### 2.1 Connect Repository
1. Go to [Netlify Dashboard](https://app.netlify.com)
2. Click "New site from Git"
3. Choose your Git provider (GitHub, GitLab, etc.)
4. Select your repository
5. Configure build settings:
   - **Build command**: Leave empty (or `echo "No build required"`)
   - **Publish directory**: `.` (root directory)
   - **Functions directory**: `netlify/functions`

### 2.2 Configure Environment Variables
In your Netlify site dashboard, go to **Site settings > Environment variables** and add:

```
CLAUDE_API_KEY=your_claude_api_key_here
AIRTABLE_API_KEY=your_airtable_api_key_here
```

**⚠️ Important**: Never commit these keys to your repository!

## 🔗 Step 3: Verify Deployment

### 3.1 Check Health Endpoint
Visit: `https://your-site-name.netlify.app/health`

You should see a JSON response with service status.

### 3.2 Test API Endpoints
- **Health Check**: `GET /health`
- **Airtable Records**: `GET /api/airtable/records?baseId=...&tableId=...`
- **Chat API**: `POST /api/chat`
- **Cache Management**: `GET /api/cache`

## 📁 File Structure Changes

Your new Netlify-compatible structure:
```
├── netlify.toml              # Netlify configuration
├── package.json              # Node.js dependencies
├── netlify/
│   └── functions/            # Serverless functions
│       ├── airtable-records.mts
│       ├── chat.mts
│       ├── health.mts
│       └── cache.mts
├── index.html               # Your dashboard (unchanged)
├── styles.css               # Your styles (unchanged)
├── script.js                # Your JavaScript (unchanged)
└── img/                     # Your images (unchanged)
```

## 🔄 Migration Summary

### What Changed:
1. **Flask server** → **Netlify serverless functions**
2. **In-memory cache** → **Netlify Blobs storage**
3. **Local .env** → **Netlify environment variables**
4. **Python dependencies** → **Node.js dependencies**

### What Stayed the Same:
- ✅ All HTML, CSS, JavaScript files
- ✅ Dashboard functionality and UI
- ✅ Airtable API integration
- ✅ Claude API integration
- ✅ All existing features

## 🛠️ Troubleshooting

### Common Issues:

1. **Environment Variables Not Working**
   - Check they're set in Netlify dashboard
   - Redeploy after adding variables

2. **Function Timeout**
   - Functions have 10-second timeout by default
   - Use background functions for longer operations

3. **CORS Issues**
   - Functions automatically handle CORS
   - Check Content-Security-Policy headers

4. **Cache Not Working**
   - Netlify Blobs may take time to provision
   - Check function logs in Netlify dashboard

### Debug Steps:
1. Check Netlify function logs
2. Test endpoints individually
3. Verify environment variables
4. Check network requests in browser dev tools

## 📊 Performance Benefits

### Netlify Advantages:
- ✅ **Global CDN**: Faster loading worldwide
- ✅ **Auto-scaling**: Handles traffic spikes
- ✅ **Zero maintenance**: No server management
- ✅ **Built-in SSL**: HTTPS by default
- ✅ **Deploy previews**: Test before going live

## 🔐 Security Features

- ✅ **Environment variables**: Secure API key storage
- ✅ **HTTPS**: SSL/TLS encryption
- ✅ **Function isolation**: Each function runs independently
- ✅ **No server access**: Reduced attack surface

## 📈 Monitoring

Monitor your deployment:
1. **Netlify Analytics**: Built-in traffic analytics
2. **Function logs**: Debug and monitor functions
3. **Health endpoint**: `/health` for uptime monitoring
4. **Cache stats**: `/api/cache` for cache monitoring

## 🚀 Next Steps

1. **Custom Domain**: Add your own domain in Netlify settings
2. **Branch Deploys**: Set up staging environments
3. **Form Handling**: Use Netlify Forms if needed
4. **Analytics**: Enable Netlify Analytics
5. **Performance**: Monitor and optimize function performance

## 📞 Support

If you encounter issues:
1. Check Netlify function logs
2. Review this guide
3. Check Netlify documentation
4. Contact support if needed

---

**🎉 Congratulations!** Your Attribution Dashboard is now running on Netlify with serverless architecture!
