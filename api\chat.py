from http.server import BaseHTTPRequestHandler
import json
import os
import requests
from datetime import datetime

# Configuration
CLAUDE_API_URL = 'https://api.anthropic.com/v1/messages'
CLAUDE_API_TIMEOUT = 30

class handler(BaseHTTPRequestHandler):
    def do_POST(self):
        try:
            # Get Claude API key
            claude_api_key = os.environ.get('CLAUDE_API_KEY')
            if not claude_api_key:
                self.send_response(500)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "CLAUDE_API_KEY environment variable is required"}).encode())
                return

            # Read request body
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            # Extract system message and user messages
            messages = data.get('messages', [])
            system_message = None
            filtered_messages = []

            for message in messages:
                if message.get('role') == 'system':
                    system_message = message.get('content')
                else:
                    filtered_messages.append(message)

            # Prepare Claude API request
            claude_request = {
                'model': data.get('model', 'claude-3-opus-20240229'),
                'max_tokens': data.get('max_tokens', 4000),
                'temperature': data.get('temperature', 0.7),
                'messages': filtered_messages
            }

            # Add system message if present
            if system_message:
                claude_request['system'] = system_message

            # Prepare headers
            headers = {
                'Content-Type': 'application/json',
                'x-api-key': claude_api_key,
                'anthropic-version': '2023-01-01'
            }

            # Forward request to Claude API
            try:
                response = requests.post(
                    CLAUDE_API_URL,
                    json=claude_request,
                    headers=headers,
                    timeout=CLAUDE_API_TIMEOUT
                )
            except requests.exceptions.Timeout:
                self.send_response(504)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Request timed out. Please try again."}).encode())
                return
            except requests.exceptions.ConnectionError:
                self.send_response(503)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "Unable to connect to AI service. Please try again."}).encode())
                return
            except requests.exceptions.RequestException as e:
                self.send_response(502)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps({"error": "AI service request failed. Please try again."}).encode())
                return

            # Check if request was successful
            if response.status_code != 200:
                self.send_response(response.status_code)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps({"error": f"Claude API returned status code {response.status_code}"}).encode())
                return

            # Return Claude API response
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response.json()).encode())

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
