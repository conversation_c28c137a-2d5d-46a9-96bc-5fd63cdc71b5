{"name": "@netlify/blobs", "version": "7.4.0", "description": "A JavaScript client for the Netlify Blob Store", "type": "module", "engines": {"node": "^14.16.0 || >=16.0.0"}, "main": "./dist/main.cjs", "module": "./dist/main.js", "types": "./dist/main.d.ts", "exports": {".": {"require": {"types": "./dist/main.d.cts", "default": "./dist/main.cjs"}, "import": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "default": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}}, "./package.json": "./package.json", "./server": {"require": {"types": "./dist/server.d.cts", "default": "./dist/server.cjs"}, "import": {"types": "./dist/server.d.ts", "default": "./dist/server.js"}, "default": {"types": "./dist/server.d.ts", "default": "./dist/server.js"}}}, "files": ["dist/**/*", "server.d.ts"], "scripts": {"build": "run-s build:*", "build:check": "tsc", "build:transpile": "node build.mjs", "dev": "node build.mjs --watch", "prepare": "husky install node_modules/@netlify/eslint-config-node/.husky/", "prepublishOnly": "npm ci && npm test", "prepack": "npm run build", "test": "run-s build format test:dev", "format": "run-s build format:check-fix:*", "format:ci": "run-s build format:check:*", "format:check-fix:lint": "run-e format:check:lint format:fix:lint", "format:check:lint": "cross-env-shell eslint $npm_package_config_eslint", "format:fix:lint": "cross-env-shell eslint --fix $npm_package_config_eslint", "format:check-fix:prettier": "run-e format:check:prettier format:fix:prettier", "format:check:prettier": "cross-env-shell prettier --check $npm_package_config_prettier", "format:fix:prettier": "cross-env-shell prettier --write $npm_package_config_prettier", "test:dev": "run-s build test:dev:*", "test:ci": "run-s build test:ci:*", "test:dev:vitest": "vitest run", "test:dev:vitest:watch": "vitest watch", "test:ci:vitest": "vitest run"}, "config": {"eslint": "--ignore-path .gitignore --cache --format=codeframe --max-warnings=0 \"{src,scripts,.github}/**/*.{js,ts,md,html}\" \"*.{js,ts,md,html}\"", "prettier": "--ignore-path .gitignore --loglevel=warn \"{src,scripts,.github}/**/*.{js,ts,md,yml,json,html}\" \"*.{js,ts,yml,json,html}\" \".*.{js,ts,yml,json,html}\" \"!**/package-lock.json\" \"!package-lock.json\""}, "keywords": [], "license": "MIT", "repository": "netlify/blobs", "bugs": {"url": "https://github.com/netlify/blobs/issues"}, "author": "Netlify Inc.", "directories": {"test": "test"}, "devDependencies": {"@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "@netlify/eslint-config-node": "^7.0.1", "c8": "^7.11.0", "esbuild": "^0.22.0", "husky": "^8.0.0", "node-fetch": "^3.3.1", "semver": "^7.5.3", "tmp-promise": "^3.0.3", "tsup": "^7.2.0", "typescript": "^5.0.0", "vitest": "^0.34.0"}}