<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Timestamp Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .footer {
            background: #16213e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-button {
            background: #e91e63;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #c2185b;
        }
        .test-results {
            background: #0f3460;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Footer Timestamp Test Page</h1>
        <p>This page tests the dynamic footer timestamp functionality.</p>
        
        <div class="footer">
            <div id="footer-timestamp">Loading data freshness...</div>
        </div>
        
        <div>
            <button class="test-button" onclick="testBasicFunctionality()">Test Basic Functionality</button>
            <button class="test-button" onclick="testDateParsing()">Test Date Parsing</button>
            <button class="test-button" onclick="testDisplayFormatting()">Test Display Formatting</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="test-results" class="test-results">
            <strong>Test Results:</strong><br>
            Ready to run tests...
        </div>
    </div>

    <script>
        // Simplified version of the FooterTimestampManager for testing
        class TestFooterTimestampManager {
            constructor() {
                this.cache = {
                    latestDate: null,
                    timestamp: null,
                    ttl: 5 * 60 * 1000
                };
            }

            parseDate(dateString, format) {
                if (!dateString) return null;

                try {
                    if (format === 'iso') {
                        return new Date(dateString);
                    } else if (format === 'us') {
                        const parts = dateString.split('/');
                        if (parts.length === 3) {
                            const month = parts[0].padStart(2, '0');
                            const day = parts[1].padStart(2, '0');
                            let year = parts[2];
                            
                            if (year.length === 2) {
                                year = '20' + year;
                            }
                            
                            return new Date(`${year}-${month}-${day}`);
                        }
                    }
                    
                    return new Date(dateString);
                } catch (error) {
                    console.warn(`Error parsing date "${dateString}":`, error);
                    return null;
                }
            }

            formatTimestampDisplay(date) {
                if (!date) {
                    return 'Data freshness unavailable';
                }

                const now = new Date();
                const diffTime = now - date;
                const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

                const dateStr = date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                });

                if (diffDays === 0) {
                    return `Data current through: ${dateStr} (today)`;
                } else if (diffDays === 1) {
                    return `Data current through: ${dateStr} (yesterday)`;
                } else if (diffDays <= 7) {
                    return `Data current through: ${dateStr} (${diffDays} days ago)`;
                } else {
                    return `Data current through: ${dateStr}`;
                }
            }

            updateFooterDisplay(testDate = null) {
                const footerElement = document.getElementById('footer-timestamp');
                if (!footerElement) {
                    return 'Footer element not found';
                }

                const date = testDate || new Date('2025-05-30'); // Test with a sample date
                const displayText = this.formatTimestampDisplay(date);
                footerElement.textContent = displayText;
                return displayText;
            }
        }

        const testManager = new TestFooterTimestampManager();

        function logResult(message) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML += '<br>' + message;
        }

        function clearResults() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<strong>Test Results:</strong><br>Ready to run tests...';
        }

        function testBasicFunctionality() {
            logResult('<br><strong>Testing Basic Functionality:</strong>');
            
            // Test 1: Footer element exists
            const footerElement = document.getElementById('footer-timestamp');
            if (footerElement) {
                logResult('✅ Footer element found');
            } else {
                logResult('❌ Footer element not found');
                return;
            }

            // Test 2: Update display
            const result = testManager.updateFooterDisplay();
            logResult(`✅ Display updated: "${result}"`);

            // Test 3: Test with different dates
            const testDates = [
                new Date(), // Today
                new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
                new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
                new Date(Date.now() - 10 * 24 * 60 * 60 * 1000) // 10 days ago
            ];

            testDates.forEach((date, index) => {
                const result = testManager.updateFooterDisplay(date);
                logResult(`✅ Test date ${index + 1}: "${result}"`);
            });
        }

        function testDateParsing() {
            logResult('<br><strong>Testing Date Parsing:</strong>');
            
            const testCases = [
                { input: '2025-05-30', format: 'iso', expected: 'valid' },
                { input: '5/30/2025', format: 'us', expected: 'valid' },
                { input: '05/30/25', format: 'us', expected: 'valid' },
                { input: 'invalid-date', format: 'iso', expected: 'invalid' },
                { input: '', format: 'iso', expected: 'null' },
                { input: null, format: 'iso', expected: 'null' }
            ];

            testCases.forEach((testCase, index) => {
                const result = testManager.parseDate(testCase.input, testCase.format);
                const isValid = result instanceof Date && !isNaN(result);
                const status = result === null ? 'null' : (isValid ? 'valid' : 'invalid');
                
                if (status === testCase.expected) {
                    logResult(`✅ Parse test ${index + 1}: "${testCase.input}" (${testCase.format}) → ${status}`);
                } else {
                    logResult(`❌ Parse test ${index + 1}: "${testCase.input}" (${testCase.format}) → ${status} (expected ${testCase.expected})`);
                }
            });
        }

        function testDisplayFormatting() {
            logResult('<br><strong>Testing Display Formatting:</strong>');
            
            const now = new Date();
            const testCases = [
                { date: now, description: 'Today' },
                { date: new Date(now.getTime() - 24 * 60 * 60 * 1000), description: 'Yesterday' },
                { date: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), description: '3 days ago' },
                { date: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), description: '10 days ago' },
                { date: null, description: 'Null date' }
            ];

            testCases.forEach((testCase, index) => {
                const result = testManager.formatTimestampDisplay(testCase.date);
                logResult(`✅ Format test ${index + 1} (${testCase.description}): "${result}"`);
            });
        }

        // Initialize with a test
        window.addEventListener('load', () => {
            logResult('Page loaded, footer timestamp system ready for testing');
            testManager.updateFooterDisplay();
        });
    </script>
</body>
</html>
