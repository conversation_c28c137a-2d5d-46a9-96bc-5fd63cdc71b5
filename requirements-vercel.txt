# Vercel Python Runtime Requirements
# Minimal dependencies for serverless functions

# HTTP requests
requests==2.31.0

# JSON handling (built-in to Python)
# datetime (built-in to Python)
# os (built-in to Python)
# hashlib (built-in to Python)
# urllib.parse (built-in to Python)
# time (built-in to Python)

# Note: Flask and Flask-CORS are not needed for Vercel serverless functions
# as we're using the BaseHTTPRequestHandler approach
