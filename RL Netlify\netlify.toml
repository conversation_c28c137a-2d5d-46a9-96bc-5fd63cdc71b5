# Netlify Configuration for Attribution Dashboard
[build]
  # Build command (none needed for static files)
  command = ""
  # Publish directory (root directory contains static files)
  publish = "."
  # Functions directory
  functions = "netlify/functions"

[functions]
  # Function timeout (max 10 seconds for serverless, 15 minutes for background)
  timeout = 30
  # Node.js version
  node_bundler = "esbuild"

# Environment variables (set these in Netlify UI)
[build.environment]
  NODE_VERSION = "18"

# Redirects and rewrites
[[redirects]]
  # API routes to serverless functions
  from = "/api/airtable/records"
  to = "/.netlify/functions/airtable-records"
  status = 200

[[redirects]]
  from = "/api/chat"
  to = "/.netlify/functions/chat"
  status = 200

[[redirects]]
  from = "/health"
  to = "/.netlify/functions/health"
  status = 200

[[redirects]]
  from = "/api/cache"
  to = "/.netlify/functions/cache"
  status = 200

[[redirects]]
  # Serve index.html for root path
  from = "/"
  to = "/index.html"
  status = 200

[[redirects]]
  # SPA fallback - serve index.html for any unmatched routes
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://code.highcharts.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' https://api.airtable.com https://api.anthropic.com"

[[headers]]
  for = "/api/*"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=3600"
