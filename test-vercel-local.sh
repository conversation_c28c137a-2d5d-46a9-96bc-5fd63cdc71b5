#!/bin/bash

# Local Vercel Testing Script
echo "🧪 Testing Vercel deployment locally..."
echo ""

# Check if vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI is not installed."
    echo "📦 Install it with: npm install -g vercel"
    exit 1
fi

echo "✅ Vercel CLI found"
echo ""

# Check if environment variables are set
echo "🔍 Checking environment variables..."
if [ -z "$CLAUDE_API_KEY" ] && [ -z "$AIRTABLE_API_KEY" ]; then
    echo "⚠️  Environment variables not set locally."
    echo "💡 You can either:"
    echo "   1. Set them in your shell: export CLAUDE_API_KEY=your_key"
    echo "   2. Use Vercel's environment variables (they'll be loaded automatically)"
    echo ""
fi

# Start local development server
echo "🚀 Starting Vercel development server..."
echo "📍 Your app will be available at: http://localhost:3000"
echo ""
echo "🔗 Test these endpoints:"
echo "   • http://localhost:3000/ (Main dashboard)"
echo "   • http://localhost:3000/health (Health check)"
echo "   • http://localhost:3000/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf"
echo ""
echo "⏹️  Press Ctrl+C to stop the server"
echo ""

# Start vercel dev
vercel dev
