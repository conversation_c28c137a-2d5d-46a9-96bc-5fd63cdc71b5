#!/bin/bash

# Vercel Environment Variables Setup Script
echo "🔧 Setting up Vercel environment variables..."
echo ""

# Check if vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI is not installed."
    echo "📦 Install it with: npm install -g vercel"
    exit 1
fi

echo "✅ Vercel CLI found"
echo ""

# Check if user is logged in
if ! vercel whoami &> /dev/null; then
    echo "🔐 Please login to Vercel first:"
    vercel login
fi

echo "👤 Logged in as: $(vercel whoami)"
echo ""

# Set CLAUDE_API_KEY
echo "🤖 Setting up Claude API Key..."
echo "📝 Please enter your Claude API key (from https://console.anthropic.com/):"
vercel env add CLAUDE_API_KEY

echo ""

# Set AIRTABLE_API_KEY  
echo "📊 Setting up Airtable API Key..."
echo "📝 Please enter your Airtable API key (from https://airtable.com/account):"
vercel env add AIRTABLE_API_KEY

echo ""
echo "✅ Environment variables configured!"
echo ""
echo "🚀 Next steps:"
echo "1. Run 'vercel --prod' to deploy"
echo "2. Test your deployment at the provided URL"
echo "3. Check /health endpoint to verify everything works"
echo ""
