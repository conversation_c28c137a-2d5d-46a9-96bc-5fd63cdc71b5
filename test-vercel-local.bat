@echo off
echo 🧪 Testing Vercel deployment locally...
echo.

REM Check if vercel CLI is installed
vercel --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Vercel CLI is not installed.
    echo 📦 Install it with: npm install -g vercel
    pause
    exit /b 1
)

echo ✅ Vercel CLI found
echo.

REM Check if environment variables are set
echo 🔍 Checking environment variables...
if "%CLAUDE_API_KEY%"=="" if "%AIRTABLE_API_KEY%"=="" (
    echo ⚠️  Environment variables not set locally.
    echo 💡 You can either:
    echo    1. Set them in your environment
    echo    2. Use Vercel's environment variables (they'll be loaded automatically)
    echo.
)

REM Start local development server
echo 🚀 Starting Vercel development server...
echo 📍 Your app will be available at: http://localhost:3000
echo.
echo 🔗 Test these endpoints:
echo    • http://localhost:3000/ (Main dashboard)
echo    • http://localhost:3000/health (Health check)
echo    • http://localhost:3000/api/airtable/records?baseId=app7ffftdM6e3yekG^&tableId=tblcdFVUC3zJrbmNf
echo.
echo ⏹️  Press Ctrl+C to stop the server
echo.

REM Start vercel dev
vercel dev
