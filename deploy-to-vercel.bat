@echo off
echo 🚀 Deploying Attribution Dashboard to Vercel...
echo.

REM Check if vercel CLI is installed
vercel --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Vercel CLI is not installed.
    echo 📦 Install it with: npm install -g vercel
    pause
    exit /b 1
)

echo ✅ Vercel CLI found
echo.

REM Check if user is logged in
vercel whoami >nul 2>&1
if errorlevel 1 (
    echo 🔐 Please login to Vercel first:
    vercel login
)

echo 👤 Logged in to Vercel
echo.

REM Check if environment variables are configured
echo 🔍 Checking environment variables...
echo 💡 Make sure you've set up:
echo    • CLAUDE_API_KEY
echo    • AIRTABLE_API_KEY
echo.
set /p env_configured="❓ Have you configured these environment variables? (y/n): "

if /i not "%env_configured%"=="y" (
    echo ⚠️  Please run the environment setup first:
    echo    setup-vercel-env.bat
    echo.
    set /p setup_now="❓ Do you want to set them up now? (y/n): "
    
    if /i "%setup_now%"=="y" (
        call setup-vercel-env.bat
    ) else (
        echo ❌ Deployment cancelled. Please set up environment variables first.
        pause
        exit /b 1
    )
)

echo.
echo 🚀 Starting deployment...
echo.

REM Deploy to production
vercel --prod

echo.
echo ✅ Deployment complete!
echo.
echo 🔗 Your app should now be live at the URL shown above
echo.
echo 🧪 Test these endpoints:
echo    • /health (Health check)
echo    • /api/airtable/records (Airtable data)
echo    • /api/chat (Claude AI chat)
echo.
echo 📊 Monitor your deployment:
echo    • Vercel Dashboard: https://vercel.com/dashboard
echo    • Function logs: Available in the dashboard
echo.
pause
