import type { Context, Config } from "@netlify/functions";
import { getStore } from "@netlify/blobs";

export default async (req: Request, context: Context) => {
  console.log(`🗄️ Cache API called: ${req.method} ${req.url}`);

  try {
    const cacheStore = getStore("airtable-cache");

    if (req.method === 'GET') {
      // Get cache statistics
      try {
        const cacheKeys = await cacheStore.list();
        const cacheStats = {
          status: 'active',
          total_keys: cacheKeys.blobs.length,
          keys: cacheKeys.blobs.map(blob => ({
            key: blob.key,
            size: blob.size,
            created: blob.created_at
          })),
          timestamp: new Date().toISOString()
        };

        console.log(`📊 Cache stats: ${cacheStats.total_keys} keys`);

        return new Response(JSON.stringify(cacheStats), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        });

      } catch (error) {
        console.error('❌ Error getting cache stats:', error);
        
        return new Response(JSON.stringify({
          error: 'Failed to get cache statistics',
          details: error instanceof Error ? error.message : 'Unknown error'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }

    } else if (req.method === 'DELETE') {
      // Clear all cache
      try {
        const cacheKeys = await cacheStore.list();
        let deletedCount = 0;

        for (const blob of cacheKeys.blobs) {
          await cacheStore.delete(blob.key);
          deletedCount++;
        }

        console.log(`🗑️ Cache cleared: ${deletedCount} keys deleted`);

        return new Response(JSON.stringify({
          status: 'cleared',
          deleted_keys: deletedCount,
          timestamp: new Date().toISOString()
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        });

      } catch (error) {
        console.error('❌ Error clearing cache:', error);
        
        return new Response(JSON.stringify({
          error: 'Failed to clear cache',
          details: error instanceof Error ? error.message : 'Unknown error'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }

    } else {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('💥 Cache API error:', error);
    
    return new Response(JSON.stringify({
      error: error instanceof Error ? error.message : 'Internal server error',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/cache"
};
