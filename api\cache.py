from http.server import BaseHTTPRequestHandler
import json
from datetime import datetime

# Simple in-memory cache for demonstration
# Note: In production, you'd want to use Redis or another persistent cache
cache_stats = {
    'total_entries': 0,
    'total_size_bytes': 0,
    'cache_enabled': True,
    'last_cleared': None
}

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            # Return cache statistics
            stats = {
                **cache_stats,
                'timestamp': datetime.now().isoformat(),
                'note': 'Serverless functions have ephemeral storage - cache is per-function instance'
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(stats).encode())

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def do_DELETE(self):
        try:
            # Clear cache (in serverless, this just resets the stats)
            global cache_stats
            cache_stats = {
                'total_entries': 0,
                'total_size_bytes': 0,
                'cache_enabled': True,
                'last_cleared': datetime.now().isoformat()
            }

            response = {
                'status': 'success',
                'message': 'Cache cleared successfully (serverless function cache is ephemeral)',
                'timestamp': datetime.now().isoformat()
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps({"error": str(e)}).encode())

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
