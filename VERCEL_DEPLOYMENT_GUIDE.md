# 🚀 Vercel Deployment Guide for Attribution Dashboard

## 📋 Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Vercel CLI**: Install globally with `npm install -g vercel`
3. **Environment Variables**: You'll need:
   - `CLAUDE_API_KEY` - Your Anthropic Claude API key
   - `AIRTABLE_API_KEY` - Your Airtable API key

## 🔧 Setup Steps

### 1. Install Vercel CLI
```bash
npm install -g vercel
```

### 2. Login to Vercel
```bash
vercel login
```

### 3. Initialize Project
```bash
vercel
```
Follow the prompts:
- Link to existing project? **N**
- Project name: **attribution-dashboard** (or your preferred name)
- Directory: **.** (current directory)
- Override settings? **N**

### 4. Set Environment Variables
```bash
vercel env add CLAUDE_API_KEY
# Paste your Claude API key when prompted

vercel env add AI<PERSON><PERSON>LE_API_KEY
# Paste your Airtable API key when prompted
```

Or set them in the Vercel dashboard:
1. Go to your project in Vercel dashboard
2. Settings → Environment Variables
3. Add both keys for Production, Preview, and Development

### 5. Deploy
```bash
vercel --prod
```

## 📁 File Structure for Vercel

```
your-project/
├── api/                    # Serverless functions
│   ├── airtable_records.py # Airtable data fetching
│   ├── chat.py            # Claude AI chat
│   ├── health.py          # Health check
│   └── cache.py           # Cache management
├── img/                   # Static images
├── index.html             # Main dashboard
├── script.js              # Frontend JavaScript
├── styles.css             # Styles
├── vercel.json            # Vercel configuration
├── package.json           # Node.js metadata
└── requirements-vercel.txt # Python dependencies
```

## 🔄 API Endpoints

After deployment, your endpoints will be:
- `https://your-app.vercel.app/` - Main dashboard
- `https://your-app.vercel.app/api/airtable/records` - Airtable data
- `https://your-app.vercel.app/api/chat` - Claude AI chat
- `https://your-app.vercel.app/health` - Health check
- `https://your-app.vercel.app/api/cache` - Cache management

## 🐛 Troubleshooting

### Common Issues:

1. **Environment Variables Not Working**
   - Check they're set for all environments (Production, Preview, Development)
   - Redeploy after adding environment variables

2. **Python Function Errors**
   - Check Vercel function logs in the dashboard
   - Ensure `requirements-vercel.txt` has all needed dependencies

3. **CORS Issues**
   - All API functions include CORS headers
   - Check browser console for specific errors

4. **Timeout Issues**
   - Vercel has a 10-second timeout for Hobby plan
   - Consider upgrading for longer timeouts if needed

### Debugging:
```bash
# View deployment logs
vercel logs

# Test locally
vercel dev
```

## 🔄 Updates

To update your deployment:
```bash
vercel --prod
```

## 📊 Monitoring

- View logs: Vercel Dashboard → Your Project → Functions tab
- Monitor performance: Vercel Dashboard → Analytics
- Check health: Visit `/health` endpoint

## 🆚 Differences from Flask Version

1. **No Flask Server**: Uses Vercel's serverless functions
2. **Stateless**: Each function call is independent
3. **Auto-scaling**: Vercel handles scaling automatically
4. **Cold Starts**: First request may be slower
5. **Timeout Limits**: 10 seconds on Hobby plan, 60 seconds on Pro

## 🎯 Next Steps

1. Test all functionality after deployment
2. Set up custom domain (optional)
3. Configure monitoring and alerts
4. Consider upgrading Vercel plan for production use
