#!/bin/bash

# Vercel Deployment Script
echo "🚀 Deploying Attribution Dashboard to Vercel..."
echo ""

# Check if vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI is not installed."
    echo "📦 Install it with: npm install -g vercel"
    exit 1
fi

echo "✅ Vercel CLI found"
echo ""

# Check if user is logged in
if ! vercel whoami &> /dev/null; then
    echo "🔐 Please login to Vercel first:"
    vercel login
fi

echo "👤 Logged in as: $(vercel whoami)"
echo ""

# Check if environment variables are configured
echo "🔍 Checking environment variables..."
echo "💡 Make sure you've set up:"
echo "   • CLAUDE_API_KEY"
echo "   • AIRTABLE_API_KEY"
echo ""
echo "❓ Have you configured these environment variables? (y/n)"
read -r env_configured

if [ "$env_configured" != "y" ] && [ "$env_configured" != "Y" ]; then
    echo "⚠️  Please run the environment setup first:"
    echo "   ./setup-vercel-env.sh"
    echo ""
    echo "❓ Do you want to set them up now? (y/n)"
    read -r setup_now
    
    if [ "$setup_now" = "y" ] || [ "$setup_now" = "Y" ]; then
        ./setup-vercel-env.sh
    else
        echo "❌ Deployment cancelled. Please set up environment variables first."
        exit 1
    fi
fi

echo ""
echo "🚀 Starting deployment..."
echo ""

# Deploy to production
vercel --prod

echo ""
echo "✅ Deployment complete!"
echo ""
echo "🔗 Your app should now be live at the URL shown above"
echo ""
echo "🧪 Test these endpoints:"
echo "   • /health (Health check)"
echo "   • /api/airtable/records (Airtable data)"
echo "   • /api/chat (Claude AI chat)"
echo ""
echo "📊 Monitor your deployment:"
echo "   • Vercel Dashboard: https://vercel.com/dashboard"
echo "   • Function logs: Available in the dashboard"
echo ""
