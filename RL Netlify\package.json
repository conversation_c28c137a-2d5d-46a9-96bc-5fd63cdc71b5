{"name": "attribution-dashboard-netlify", "version": "1.0.0", "description": "Attribution Dashboard deployed on Netlify with serverless functions", "main": "index.html", "scripts": {"dev": "netlify dev", "build": "echo 'No build step required for static files'", "deploy": "netlify deploy --prod", "test": "echo 'No tests specified'"}, "dependencies": {"@netlify/functions": "^2.0.0", "@netlify/blobs": "^7.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/attribution-dashboard.git"}, "keywords": ["analytics", "dashboard", "attribution", "netlify", "serverless"], "author": "Your Name", "license": "MIT"}