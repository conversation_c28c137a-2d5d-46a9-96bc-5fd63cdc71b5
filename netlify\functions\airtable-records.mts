import type { Context, Config } from "@netlify/functions";

// Configuration constants
const AIRTABLE_BASE_URL = 'https://api.airtable.com/v0';
const MAX_RECORDS_PER_REQUEST = 100;
const MAX_TOTAL_RECORDS = 10000;
const MAX_PAGINATION_PAGES = 50;

// Table configurations
const FRESH_TABLES = {
  'ghl': {
    'id': 'tblcdFVUC3zJrbmNf',
    'name': 'Fresh GHL',
    'date_field': 'Date Created',
    'sort_direction': 'desc'
  },
  'pos': {
    'id': 'tblHyyZHUsTdEb3BL',
    'name': 'Fresh POS',
    'date_field': 'Created',
    'sort_direction': 'desc'
  },
  'meta_ads': {
    'id': 'tbl7mWcQBNA2TQAjc',
    'name': 'Fresh Meta Ads',
    'date_field': 'Reporting ends',
    'sort_direction': 'desc'
  },
  'meta_ads_summary': {
    'id': 'tblIQXVSwtwq1P4W7',
    'name': 'Meta Ads Summary',
    'date_field': 'Reporting ends',
    'sort_direction': 'desc'
  },
  'meta_ads_simplified': {
    'id': 'tblA6ABFBTURfyZx9',
    'name': 'Meta Ads Simplified',
    'date_field': 'period',
    'sort_direction': 'desc'
  },
  'google_ads': {
    'id': 'tblRBXdh6L6zm9CZn',
    'name': 'Fresh Google Ads',
    'date_field': 'Date',
    'sort_direction': 'desc'
  }
};

interface AirtableRecord {
  id: string;
  fields: Record<string, any>;
  createdTime: string;
}

interface AirtableResponse {
  records: AirtableRecord[];
  offset?: string;
}

async function fetchAirtableRecords(
  baseId: string,
  tableId: string,
  filterFormula?: string,
  maxRecords: number = 1000
): Promise<{ records: AirtableRecord[]; totalRecords: number }> {
  const AIRTABLE_API_KEY = Netlify.env.get('AIRTABLE_API_KEY');
  
  if (!AIRTABLE_API_KEY) {
    throw new Error('AIRTABLE_API_KEY environment variable is required');
  }

  const allRecords: AirtableRecord[] = [];
  let offset: string | undefined;
  let pageCount = 0;
  let totalFetched = 0;

  console.log(`🔍 Starting Airtable fetch: baseId=${baseId}, tableId=${tableId}, maxRecords=${maxRecords}`);

  while (pageCount < MAX_PAGINATION_PAGES && totalFetched < Math.min(maxRecords, MAX_TOTAL_RECORDS)) {
    const params = new URLSearchParams({
      pageSize: Math.min(MAX_RECORDS_PER_REQUEST, maxRecords - totalFetched).toString()
    });

    if (filterFormula) {
      params.append('filterByFormula', filterFormula);
    }

    if (offset) {
      params.append('offset', offset);
    }

    const url = `${AIRTABLE_BASE_URL}/${baseId}/${tableId}?${params.toString()}`;
    
    console.log(`📡 Fetching page ${pageCount + 1}: ${url}`);

    try {
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${AIRTABLE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        signal: AbortSignal.timeout(15000) // 15 second timeout
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Airtable API error: ${response.status} ${response.statusText} - ${errorText}`);
        throw new Error(`Airtable API error: ${response.status} ${response.statusText}`);
      }

      const data: AirtableResponse = await response.json();
      
      allRecords.push(...data.records);
      totalFetched += data.records.length;
      pageCount++;

      console.log(`✅ Page ${pageCount} fetched: ${data.records.length} records (total: ${totalFetched})`);

      // Check if we have more pages
      if (!data.offset || totalFetched >= maxRecords) {
        console.log(`🏁 Fetch complete: ${totalFetched} total records`);
        break;
      }

      offset = data.offset;

      // Small delay between requests to be respectful to Airtable API
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      console.error(`💥 Error fetching page ${pageCount + 1}:`, error);
      throw error;
    }
  }

  return {
    records: allRecords,
    totalRecords: totalFetched
  };
}

export default async (req: Request, context: Context) => {
  console.log(`🚀 Airtable Records API called: ${req.method} ${req.url}`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    const url = new URL(req.url);
    const baseId = url.searchParams.get('baseId');
    const tableId = url.searchParams.get('tableId');
    const filterFormula = url.searchParams.get('filterByFormula');
    const maxRecordsParam = url.searchParams.get('maxRecords');
    const maxRecords = maxRecordsParam ? parseInt(maxRecordsParam, 10) : 1000;

    // Validate required parameters
    if (!baseId || !tableId) {
      return new Response(JSON.stringify({
        error: 'Missing required parameters: baseId and tableId are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log(`📋 Request params: baseId=${baseId}, tableId=${tableId}, maxRecords=${maxRecords}`);

    // Fetch records from Airtable
    const result = await fetchAirtableRecords(baseId, tableId, filterFormula || undefined, maxRecords);

    const response = {
      records: result.records,
      totalRecords: result.totalRecords,
      timestamp: new Date().toISOString(),
      baseId,
      tableId,
      maxRecords,
      filterFormula: filterFormula || null
    };

    console.log(`✅ Successfully fetched ${result.totalRecords} records`);

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('💥 Unexpected error in airtable-records function:', error);
    
    return new Response(JSON.stringify({
      error: error instanceof Error ? error.message : 'Internal server error',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/airtable/records"
};
