@echo off
echo 🔧 Setting up Vercel environment variables...
echo.

REM Check if vercel CLI is installed
vercel --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Vercel CLI is not installed.
    echo 📦 Install it with: npm install -g vercel
    pause
    exit /b 1
)

echo ✅ Vercel CLI found
echo.

REM Check if user is logged in
vercel whoami >nul 2>&1
if errorlevel 1 (
    echo 🔐 Please login to Vercel first:
    vercel login
)

echo 👤 Logged in to Vercel
echo.

REM Set CLAUDE_API_KEY
echo 🤖 Setting up Claude API Key...
echo 📝 Please enter your Claude API key (from https://console.anthropic.com/):
vercel env add CLAUDE_API_KEY

echo.

REM Set AIRTABLE_API_KEY  
echo 📊 Setting up Airtable API Key...
echo 📝 Please enter your Airtable API key (from https://airtable.com/account):
vercel env add AIRTABLE_API_KEY

echo.
echo ✅ Environment variables configured!
echo.
echo 🚀 Next steps:
echo 1. Run 'vercel --prod' to deploy
echo 2. Test your deployment at the provided URL
echo 3. Check /health endpoint to verify everything works
echo.
pause
