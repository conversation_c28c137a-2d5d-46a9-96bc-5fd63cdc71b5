from http.server import BaseHTTPRequestHandler
import json
import os
import requests
from datetime import datetime

# Configuration
AIRTABLE_BASE_URL = 'https://api.airtable.com/v0'

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        try:
            health_status = {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '2.0.0',
                'environment': 'vercel',
                'components': {}
            }

            # Check environment variables
            try:
                claude_api_key = os.environ.get('CLAUDE_API_KEY')
                airtable_api_key = os.environ.get('AIRTABLE_API_KEY')
                
                if claude_api_key and airtable_api_key:
                    health_status['components']['environment'] = {
                        'status': 'healthy',
                        'message': 'All required environment variables are set'
                    }
                else:
                    health_status['components']['environment'] = {
                        'status': 'unhealthy',
                        'message': 'Missing required environment variables'
                    }
                    health_status['status'] = 'unhealthy'
            except Exception as e:
                health_status['components']['environment'] = {
                    'status': 'error',
                    'message': f'Environment check failed: {str(e)}'
                }
                health_status['status'] = 'unhealthy'

            # Test Airtable connectivity (optional quick test)
            try:
                airtable_api_key = os.environ.get('AIRTABLE_API_KEY')
                if airtable_api_key:
                    # Quick connectivity test with short timeout
                    test_url = f"{AIRTABLE_BASE_URL}/meta"
                    test_headers = {'Authorization': f'Bearer {airtable_api_key}'}
                    
                    test_response = requests.get(test_url, headers=test_headers, timeout=5)
                    
                    if test_response.status_code in [200, 401, 403]:
                        health_status['components']['airtable'] = {
                            'status': 'healthy',
                            'message': 'Airtable API is reachable'
                        }
                    else:
                        health_status['components']['airtable'] = {
                            'status': 'warning',
                            'message': f'Airtable API returned status {test_response.status_code}'
                        }
                else:
                    health_status['components']['airtable'] = {
                        'status': 'warning',
                        'message': 'Airtable API key not configured'
                    }
            except requests.exceptions.Timeout:
                health_status['components']['airtable'] = {
                    'status': 'warning',
                    'message': 'Airtable API timeout (may be slow)'
                }
            except Exception as e:
                health_status['components']['airtable'] = {
                    'status': 'warning',
                    'message': f'Airtable connectivity test failed: {str(e)}'
                }

            # Test Claude API connectivity
            try:
                claude_api_key = os.environ.get('CLAUDE_API_KEY')
                if claude_api_key:
                    # Quick connectivity test
                    test_headers = {
                        'x-api-key': claude_api_key,
                        'anthropic-version': '2023-01-01'
                    }
                    
                    # Just test if we can reach the API (don't make actual request)
                    health_status['components']['claude'] = {
                        'status': 'healthy',
                        'message': 'Claude API key is configured'
                    }
                else:
                    health_status['components']['claude'] = {
                        'status': 'warning',
                        'message': 'Claude API key not configured'
                    }
            except Exception as e:
                health_status['components']['claude'] = {
                    'status': 'warning',
                    'message': f'Claude API check failed: {str(e)}'
                }

            # Determine overall status
            component_statuses = [comp['status'] for comp in health_status['components'].values()]
            if 'error' in component_statuses or health_status['status'] == 'unhealthy':
                health_status['status'] = 'unhealthy'
                status_code = 503
            elif 'warning' in component_statuses:
                health_status['status'] = 'degraded'
                status_code = 200
            else:
                health_status['status'] = 'healthy'
                status_code = 200

            # Send response
            self.send_response(status_code)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(health_status).encode())

        except Exception as e:
            error_response = {
                'status': 'error',
                'timestamp': datetime.now().isoformat(),
                'message': f'Health check failed: {str(e)}'
            }
            
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(error_response).encode())

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
