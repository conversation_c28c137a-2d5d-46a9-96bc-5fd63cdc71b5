{"version": 2, "name": "attribution-dashboard", "builds": [{"src": "api/**/*.py", "use": "@vercel/python"}], "routes": [{"src": "/api/airtable/records", "dest": "/api/airtable_records.py"}, {"src": "/api/chat", "dest": "/api/chat.py"}, {"src": "/health", "dest": "/api/health.py"}, {"src": "/api/cache", "dest": "/api/cache.py"}, {"src": "/debug-airtable", "dest": "/debug_airtable_test.html"}, {"src": "/(.*)", "dest": "/$1"}], "functions": {"api/**/*.py": {"runtime": "python3.9"}}, "env": {"CLAUDE_API_KEY": "@claude_api_key", "AIRTABLE_API_KEY": "@airtable_api_key"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}