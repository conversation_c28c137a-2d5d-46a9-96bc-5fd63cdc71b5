import type { Context, Config } from "@netlify/functions";

export default async (req: Request, context: Context) => {
  console.log(`🏥 Health check called: ${req.method} ${req.url}`);

  if (req.method !== 'GET') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    const timestamp = new Date().toISOString();
    
    // Check environment variables
    const claudeApiKey = Netlify.env.get('CLAUDE_API_KEY');
    const airtableApiKey = Netlify.env.get('AIRTABLE_API_KEY');
    
    const envStatus = {
      claude_api_key: claude<PERSON><PERSON><PERSON><PERSON> ? 'configured' : 'missing',
      airtable_api_key: airtableApiKey ? 'configured' : 'missing'
    };

    // Test Airtable API connectivity
    let airtableStatus = 'unknown';
    let airtableError = null;
    
    if (airtableApiKey) {
      try {
        // Test with a simple request to Airtable API
        const testResponse = await fetch('https://api.airtable.com/v0/meta/bases', {
          headers: {
            'Authorization': `Bearer ${airtableApiKey}`,
            'Content-Type': 'application/json'
          },
          signal: AbortSignal.timeout(10000) // 10 second timeout
        });
        
        if (testResponse.ok) {
          airtableStatus = 'connected';
        } else {
          airtableStatus = 'error';
          airtableError = `HTTP ${testResponse.status}`;
        }
      } catch (error) {
        airtableStatus = 'error';
        airtableError = error instanceof Error ? error.message : 'Unknown error';
      }
    } else {
      airtableStatus = 'not_configured';
    }

    // Test Claude API connectivity
    let claudeStatus = 'unknown';
    let claudeError = null;
    
    if (claudeApiKey) {
      try {
        // Test with a simple request to Claude API (just check if endpoint is reachable)
        const testResponse = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: {
            'x-api-key': claudeApiKey,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
          },
          body: JSON.stringify({
            model: 'claude-3-sonnet-20240229',
            max_tokens: 1,
            messages: [{ role: 'user', content: 'test' }]
          }),
          signal: AbortSignal.timeout(10000) // 10 second timeout
        });
        
        // Even if the request fails due to content, a 400 means the API is reachable
        if (testResponse.status === 400 || testResponse.status === 200) {
          claudeStatus = 'connected';
        } else if (testResponse.status === 401) {
          claudeStatus = 'auth_error';
          claudeError = 'Invalid API key';
        } else {
          claudeStatus = 'error';
          claudeError = `HTTP ${testResponse.status}`;
        }
      } catch (error) {
        claudeStatus = 'error';
        claudeError = error instanceof Error ? error.message : 'Unknown error';
      }
    } else {
      claudeStatus = 'not_configured';
    }

    // Determine overall health
    const isHealthy = airtableStatus === 'connected' && 
                     (claudeStatus === 'connected' || claudeStatus === 'auth_error');

    const healthData = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp,
      version: '2.0.0',
      environment: 'netlify',
      services: {
        airtable: {
          status: airtableStatus,
          error: airtableError
        },
        claude: {
          status: claudeStatus,
          error: claudeError
        }
      },
      environment_variables: envStatus,
      uptime: process.uptime ? `${Math.floor(process.uptime())}s` : 'unknown',
      memory: process.memoryUsage ? process.memoryUsage() : 'unknown'
    };

    console.log(`✅ Health check completed: ${healthData.status}`);

    return new Response(JSON.stringify(healthData), {
      status: isHealthy ? 200 : 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('💥 Health check error:', error);
    
    return new Response(JSON.stringify({
      status: 'error',
      timestamp: new Date().toISOString(),
      message: error instanceof Error ? error.message : 'Health check failed'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/health"
};
