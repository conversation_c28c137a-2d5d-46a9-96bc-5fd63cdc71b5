# Development Server

This folder contains the development version of the Attribution Dashboard server.

## Files

- **`server_dev.py`** - Development server with additional debugging features
- **`README.md`** - This file

## Usage

### Running the Development Server

```bash
cd tests
python server_dev.py
```

The development server will start on **http://localhost:8001** (different port from production).

### Features

The development server includes:

- **Enhanced logging** - More verbose debug output
- **Development mode** - Flask debug mode enabled
- **Port 8001** - Runs on different port to avoid conflicts with production
- **Additional debugging** - Extra error handling and debug endpoints

### Important Notes

⚠️ **FOR TESTING ONLY** - Do not use this server in production!

🔒 **Security** - The development server has debug mode enabled and should never be exposed to external networks.

📁 **File Serving** - The development server serves files from the parent directory (`..`) to access the main application files.

## Production Server

For production use, always use the main production server:

```bash
# From the root directory
python start_server.py
```

Or directly:

```bash
python server.py
```

The production server runs on **http://localhost:8000** and includes:

- Production-ready WSGI servers (Waitress/Gunicorn)
- Security hardening
- Optimized performance
- Proper error handling
- No debug mode

## Environment Variables

Both servers require the same environment variables:

```bash
CLAUDE_API_KEY=your_claude_api_key
AIRTABLE_API_KEY=your_airtable_api_key
```

Create a `.env` file in the root directory with these values.
