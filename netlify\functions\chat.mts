import type { Context, Config } from "@netlify/functions";

const CLAUDE_API_URL = 'https://api.anthropic.com/v1/messages';

export default async (req: Request, context: Context) => {
  console.log(`💬 Chat API called: ${req.method} ${req.url}`);

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    const claudeApiKey = Netlify.env.get('CLAUDE_API_KEY');
    
    if (!claudeApiKey) {
      return new Response(JSON.stringify({
        error: 'Claude API key not configured'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const requestData = await req.json();
    
    if (!requestData.message) {
      return new Response(JSON.stringify({
        error: 'Message is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log(`📝 Processing chat message: ${requestData.message.substring(0, 100)}...`);

    // Prepare Claude API request
    const claudeRequest = {
      model: requestData.model || 'claude-3-sonnet-20240229',
      max_tokens: requestData.max_tokens || 1000,
      messages: [
        {
          role: 'user',
          content: requestData.message
        }
      ]
    };

    // Add system message if provided
    if (requestData.system) {
      claudeRequest.system = requestData.system;
    }

    console.log(`🤖 Sending request to Claude API...`);

    // Make request to Claude API
    const claudeResponse = await fetch(CLAUDE_API_URL, {
      method: 'POST',
      headers: {
        'x-api-key': claudeApiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(claudeRequest),
      signal: AbortSignal.timeout(30000) // 30 second timeout
    });

    if (!claudeResponse.ok) {
      const errorText = await claudeResponse.text();
      console.error(`❌ Claude API error: ${claudeResponse.status} ${claudeResponse.statusText} - ${errorText}`);
      
      return new Response(JSON.stringify({
        error: `Claude API error: ${claudeResponse.status} ${claudeResponse.statusText}`,
        details: errorText
      }), {
        status: claudeResponse.status,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const claudeData = await claudeResponse.json();
    
    console.log(`✅ Claude API response received`);

    // Extract the response text
    const responseText = claudeData.content?.[0]?.text || 'No response from Claude';

    const response = {
      response: responseText,
      model: claudeData.model,
      usage: claudeData.usage,
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('💥 Chat API error:', error);
    
    return new Response(JSON.stringify({
      error: error instanceof Error ? error.message : 'Internal server error',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const config: Config = {
  path: "/api/chat"
};
