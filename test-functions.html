<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netlify Functions Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a2e;
            color: white;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .result {
            background: #000;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left: 4px solid #10b981; }
        .error { border-left: 4px solid #ef4444; }
    </style>
</head>
<body>
    <h1>🧪 Netlify Functions Test Page</h1>
    <p>Use this page to test if your serverless functions are working correctly.</p>

    <div class="test-section">
        <h2>1. Health Check</h2>
        <button onclick="testHealth()">Test /health</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Airtable Records</h2>
        <button onclick="testAirtable()">Test /api/airtable/records</button>
        <div id="airtable-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Cache Management</h2>
        <button onclick="testCache()">Test /api/cache</button>
        <div id="cache-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Function URLs</h2>
        <p>Your functions should be available at:</p>
        <ul>
            <li><a href="/health" target="_blank">/health</a></li>
            <li><a href="/.netlify/functions/health" target="_blank">/.netlify/functions/health</a></li>
            <li><a href="/.netlify/functions/airtable-records" target="_blank">/.netlify/functions/airtable-records</a></li>
            <li><a href="/.netlify/functions/cache" target="_blank">/.netlify/functions/cache</a></li>
        </ul>
    </div>

    <script>
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.textContent = 'Testing health endpoint...';
            
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.textContent = `Status: ${response.status}\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function testAirtable() {
            const resultDiv = document.getElementById('airtable-result');
            resultDiv.textContent = 'Testing Airtable endpoint...';
            
            try {
                const url = '/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=5';
                const response = await fetch(url);
                const data = await response.json();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.textContent = `Status: ${response.status}\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function testCache() {
            const resultDiv = document.getElementById('cache-result');
            resultDiv.textContent = 'Testing cache endpoint...';
            
            try {
                const response = await fetch('/api/cache');
                const data = await response.json();
                
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
                resultDiv.textContent = `Status: ${response.status}\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        // Auto-test health on page load
        window.addEventListener('load', () => {
            setTimeout(testHealth, 1000);
        });
    </script>
</body>
</html>
